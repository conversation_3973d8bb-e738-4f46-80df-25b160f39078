package com.tool.converge.business.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptQueryParamsBO;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptSaveBO;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptUpdateBO;
import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptDetailVO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptPageVO;
import com.tool.converge.repository.domain.system.vo.OrganizationTreeVO;

import java.util.List;

/**
 * <p>
 * 钉钉部门 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
public interface DingTalkDeptService extends IService<DingTalkDeptDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(DingTalkDeptSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(DingTalkDeptUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    DingTalkDeptDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<DingTalkDeptPageVO> getPageInfo(DingTalkDeptQueryParamsBO queryParamsBO);

    /**
     * 获取所有部门
     *
     * @return
     */
    List<DingTalkDeptDetailVO> allDept();

    /**
     * 获取组织架构
     *
     * @return
     */
    List<OrganizationTreeVO> getOrganizationTree();
}
