package com.tool.converge.repository.domain.alert.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警事件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26 16:34:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_alert_event")
@Schema(name = "AlertEventDO对象", description = "预警事件")
public class AlertEventDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "预警事件")
    @TableId("`id`")
    private Long id;

    @Schema(description = "预警事件编号，SDK生成")
    @TableField("`event_id`")
    private String eventId;

    @Schema(description = "系统编号")
    @TableField("`system_code`")
    private String systemCode;

    @Schema(description = "模型编号")
    @TableField("`model_code`")
    private String modelCode;

    @Schema(description = "第三方名称")
    @TableField("`platform_name`")
    private String platformName;

    @Schema(description = "判断唯一字段")
    @TableField("`md5`")
    private String md5;

    @Schema(description = "期次")
    @TableField("`period`")
    private String period;

    @Schema(description = "原因")
    @TableField("`reason`")
    private String reason;

    @Schema(description = "处理状态")
    @TableField("`event_status`")
    private String eventStatus;

    @Schema(description = "业务唯一编号")
    @TableField("`service_no`")
    private String serviceNo;

    @Schema(description = "指标值")
    @TableField("`index_value`")
    private String indexValue;

    @Schema(description = "拓展字段")
    @TableField("`payload`")
    private String payload;

    @Schema(description = "状态值， 例如：单状态/还款状态/ 支付状态/推送状态")
    @TableField("`state`")
    private String state;

    @Schema(description = "处理状态：WAITING=等待处理，PROCESSING=处理中，FINISH=处理完成")
    @TableField("`handle_status`")
    private String handleStatus;

    @Schema(description = "问题归因")
    @TableField("`problem_result`")
    private String problemResult;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    @TableField("`related`")
    private Boolean related;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @Schema(description = "预警名称（对应t_alert_model.model_name）")
    @TableField("`model_name`")
    private String modelName;

    @Schema(description = "业务类型（对应t_alert_model.business_type）")
    @TableField("`business_type`")
    private String businessType;

    @Schema(description = "预警类型（对应t_alert_model.alert_type）")
    @TableField("`alert_type`")
    private String alertType;

    @Schema(description = "系统名称（对应t_data_source.system_name）")
    @TableField("`system_name`")
    private String systemName;

    @Schema(description = "预警级别")
    @TableField(select = false)
    private String alertLevel;

    @Schema(description = "开始时间")
    @TableField(select = false)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @TableField(select = false)
    private LocalDateTime endTime;

    @Schema(description = "主键ids")
    @TableField(select = false)
    private List<Long> ids;
}
