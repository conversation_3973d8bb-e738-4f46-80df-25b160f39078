package com.tool.converge.repository.domain.alert.vo;

import com.tool.converge.repository.domain.alert.db.AlertModelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
  * <AUTHOR> 
  * @date 2025/8/5 17:27
  * @Description TODO
  * @param TODO
  * @MethodName   
  */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class AlertModelExtendPageVO extends AlertModelPageVO{

    @Schema(description = "预警配置Id")
    private Long configId;

    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;


    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;


    @Schema(description = "预警内容")
    private String warnContent;

    @Schema(description = "预警级别")
    private String warnLevel;


    @Schema(description = "钉钉群地址")
    private String webhook;

    @Schema(description = "通知人员ID")
    private List<Long> notifiers;

    @Schema(description = "规则ID")
    private List<Long> ruleIds;

    public static AlertModelExtendPageVO of(AlertModelDO entity){
        if(entity == null){
            return null;
        }
        AlertModelExtendPageVO pageVO = new AlertModelExtendPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }
}
