package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警事件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 09:42:33
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertEventUpdateBO对象", description = "预警事件")
public class AlertEventUpdateBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "预警事件")
    private Long id;

    @Schema(description = "预警事件编号，SDK生成")
    private String eventId;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "模型编号")
    private String modelCode;

    @Schema(description = "第三方名称")
    private String platformName;

    @Schema(description = "期次")
    private String period;

    @Schema(description = "原因")
    private String reason;

    @Schema(description = "处理状态")
    private String eventStatus;

    @Schema(description = "业务唯一编号")
    private String serviceNo;

    @Schema(description = "指标值")
    private String indexValue;

    @Schema(description = "拓展字段")
    private String payload;

    @Schema(description = "状态值， 例如：单状态/还款状态/ 支付状态/推送状态")
    private String state;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

}
