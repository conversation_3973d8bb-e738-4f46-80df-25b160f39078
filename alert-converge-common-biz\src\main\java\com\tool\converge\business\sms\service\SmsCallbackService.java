package com.tool.converge.business.sms.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.tool.converge.repository.domain.sms.SmsCallbackRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 短信回调处理服务
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Service
public class SmsCallbackService {

    /**
     * 处理短信回调通知
     *
     * @param request 回调请求
     * @return 处理结果，true-成功，false-失败
     */
    public boolean handleCallback(SmsCallbackRequest request) {
        try {
            // 参数验证
            if (request == null) {
                log.error("短信回调处理失败：请求对象为空");
                return false;
            }

            log.info("收到短信回调通知：{}", JSON.toJSONString(request));

            // 验证必要参数
            if (StrUtil.isBlank(request.getMsgId())) {
                log.warn("短信回调参数错误：msgId为空，回调数据：{}", JSON.toJSONString(request));
                return false;
            }

            if (StrUtil.isBlank(request.getPhone())) {
                log.warn("短信回调参数错误：phone为空，msgId：{}", request.getMsgId());
                return false;
            }

            if (StrUtil.isBlank(request.getState())) {
                log.warn("短信回调参数错误：state为空，msgId：{}，phone：{}",
                        request.getMsgId(), request.getPhone());
                return false;
            }

            // 根据状态处理不同的业务逻辑并返回相应结果
            try {
                switch (request.getState()) {
                    case "DELIVRD_SUCCESS":
                        handleDeliverySuccess(request);
                        log.info("短信回调处理成功：投递成功，msgId：{}，phone：{}",
                                request.getMsgId(), request.getPhone());
                        return true;

                    case "DELIVRD_FAIL":
                        handleDeliveryFail(request);
                        log.warn("短信回调处理完成：投递失败，msgId：{}，phone：{}",
                                request.getMsgId(), request.getPhone());
                        return false;

                    case "SEND_FAIL":
                        handleSendFail(request);
                        log.error("短信回调处理完成：发送失败，msgId：{}，phone：{}",
                                request.getMsgId(), request.getPhone());
                        return false;

                    default:
                        log.warn("未知的短信状态：{}，msgId：{}，phone：{}",
                                request.getState(), request.getMsgId(), request.getPhone());
                        return false;
                }
            } catch (Exception e) {
                log.error("处理短信回调业务逻辑异常，msgId：{}，phone：{}，state：{}",
                        request.getMsgId(), request.getPhone(), request.getState(), e);
                return false;
            }

        } catch (Exception e) {
            log.error("处理短信回调异常，请求数据：{}",
                    request != null ? JSON.toJSONString(request) : "null", e);
            return false;
        }
    }

    /**
     * 处理投递成功回调
     *
     * @param request 回调请求
     */
    private void handleDeliverySuccess(SmsCallbackRequest request) {
        try {
            log.info("短信投递成功 - msgId: {}, phone: {}, receiveTime: {}, channel: {}({})",
                    request.getMsgId(),
                    request.getPhone(),
                    request.getReceiveTime(),
                    request.getSmsChannelCode(),
                    request.getSmsChannelCodeDesc());

            // TODO: 可以在这里添加业务逻辑，比如：
            // 1. 更新数据库中的短信发送状态
            // 2. 统计短信发送成功率
            // 3. 触发后续业务流程
            // 4. 发送成功通知等

            // 如果有用户回传数据，可以根据callData进行特定处理
            if (StrUtil.isNotBlank(request.getCallData())) {
                log.info("用户回传数据：{}", request.getCallData());
                // 根据callData进行特定的业务处理
            }

        } catch (Exception e) {
            log.error("处理短信投递成功回调异常，msgId：{}，phone：{}",
                    request.getMsgId(), request.getPhone(), e);
        }
    }

    /**
     * 处理投递失败回调
     *
     * @param request 回调请求
     */
    private void handleDeliveryFail(SmsCallbackRequest request) {
        try {
            log.warn("短信投递失败 - msgId: {}, phone: {}, stateMsg: {}, channel: {}({})",
                    request.getMsgId(),
                    request.getPhone(),
                    request.getStateMsg(),
                    request.getSmsChannelCode(),
                    request.getSmsChannelCodeDesc());

            // TODO: 可以在这里添加失败处理逻辑，比如：
            // 1. 记录失败原因
            // 2. 触发重试机制
            // 3. 发送失败告警
            // 4. 统计失败率等

        } catch (Exception e) {
            log.error("处理短信投递失败回调异常，msgId：{}，phone：{}",
                    request.getMsgId(), request.getPhone(), e);
        }
    }

    /**
     * 处理发送失败回调
     *
     * @param request 回调请求
     */
    private void handleSendFail(SmsCallbackRequest request) {
        try {
            log.error("短信发送失败 - msgId: {}, phone: {}, stateMsg: {}, channel: {}({})",
                    request.getMsgId(),
                    request.getPhone(),
                    request.getStateMsg(),
                    request.getSmsChannelCode(),
                    request.getSmsChannelCodeDesc());

            // TODO: 可以在这里添加发送失败处理逻辑，比如：
            // 1. 记录发送失败原因
            // 2. 尝试使用其他通道重发
            // 3. 发送紧急告警
            // 4. 通知相关人员等

        } catch (Exception e) {
            log.error("处理短信发送失败回调异常，msgId：{}，phone：{}",
                    request.getMsgId(), request.getPhone(), e);
        }
    }
}
