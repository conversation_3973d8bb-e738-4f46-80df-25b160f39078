package com.tool.converge.repository.domain.alert.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;


/**
 * <p>
 * 预警事件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12 09:42:33
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertEventPageVO对象", description = "预警事件")
public class AlertEventPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "预警事件")
    private Long id;

    @Schema(description = "预警事件编号，SDK生成")
    private String eventId;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "模型编号")
    private String modelCode;

    @Schema(description = "第三方名称")
    private String platformName;

    @Schema(description = "期次")
    private String period;

    @Schema(description = "原因")
    private String reason;

    @Schema(description = "处理状态")
    private String eventStatus;

    @Schema(description = "业务唯一编号")
    private String serviceNo;

    @Schema(description = "指标值")
    private String indexValue;

    @Schema(description = "拓展字段")
    private String payload;

    @Schema(description = "状态值， 例如：单状态/还款状态/ 支付状态/推送状态")
    private String state;

    @Schema(description = "是否关联规则 0为不关联，1为关联")
    private Boolean related;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "预警级别")
    private String alertLevel;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "预警名称")
    private String modelName;

    public static AlertEventPageVO of(AlertEventDO entity){
        if(entity == null){
            return null;
        }
        AlertEventPageVO pageVO = new AlertEventPageVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
