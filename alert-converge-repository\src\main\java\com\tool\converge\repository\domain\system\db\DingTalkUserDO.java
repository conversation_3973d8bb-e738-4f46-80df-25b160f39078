package com.tool.converge.repository.domain.system.db;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 钉钉用户
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_ding_talk_user")
@Schema(name = "DingTalkUserDO对象", description = "钉钉用户")
public class DingTalkUserDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    @TableId("`id`")
    private Long id;

    @Schema(description = "用户id")
    @TableField("`user_id`")
    private String userId;

    @Schema(description = "部门id")
    @TableField("`dept_id`")
    private Long deptId;

    @Schema(description = "部门名称")
    @TableField("`dept_name`")
    private String deptName;

    @Schema(description = "姓名")
    @TableField("`name`")
    private String name;

    @Schema(description = "岗位")
    @TableField("`post`")
    private String post;

    @Schema(description = "手机")
    @TableField("`mobile`")
    private String mobile;

    @Schema(description = "邮箱")
    @TableField("`email`")
    private String email;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 null已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic(value = "0", delval = "NULL")
    private Boolean deleted;


}
