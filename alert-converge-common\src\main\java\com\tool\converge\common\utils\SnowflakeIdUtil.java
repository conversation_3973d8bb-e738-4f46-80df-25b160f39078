package com.tool.converge.common.utils;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;

import java.util.Random;

/**
 * @className: SnowflakeIdUtil
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @description: 雪花ID工具类
 * @date: 2025/3/8 15:37
 * @version: 1.0
 */
public class SnowflakeIdUtil {

    /**
     * 创建一个全局单例的Snowflake对象
     */
    private static final Snowflake SNOWFLAKE = IdUtil.getSnowflake(generateRandomId(), generateRandomId());

    /**
     * 生成长整型的雪花ID
     *
     * @return 长整型雪花ID
     */
    public static long generateId() {
        return SNOWFLAKE.nextId();
    }

    /**
     * 生成字符串类型的雪花ID
     *
     * @return 字符串类型的雪花ID
     */
    public static String generateIdStr() {
        return SNOWFLAKE.nextIdStr();
    }

    /**
     * 生成指定范围内的随机ID
     *
     * @return 随机ID
     */
    private static long generateRandomId() {
        Random random = new Random();
        return 1 + random.nextInt(32 - 1);
    }

}
