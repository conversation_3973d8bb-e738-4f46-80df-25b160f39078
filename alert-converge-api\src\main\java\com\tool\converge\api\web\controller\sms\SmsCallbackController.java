package com.tool.converge.api.web.controller.sms;

import com.hzed.structure.log.annotation.PrintLog;
import com.tool.converge.business.sms.service.SmsCallbackService;
import com.tool.converge.repository.domain.sms.SmsCallbackRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 短信回调通知控制器
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@RestController
@RequestMapping("/api/sms")
@Tag(name = "短信回调通知", description = "短信服务回调通知接口")
public class SmsCallbackController {

    @Resource
    private SmsCallbackService smsCallbackService;

    @PrintLog("短信回调通知接口")
    @PostMapping("/callback")
    @Operation(summary = "短信回调通知接口", description = "接收短信服务商的回调通知")
    public ResponseEntity<Void> callback(@RequestBody SmsCallbackRequest request) {
        try {
            // 处理回调通知
            boolean success = smsCallbackService.handleCallback(request);

            // 记录处理结果
            if (success) {
                log.info("短信回调处理成功，msgId：{}，phone：{}，state：{}",
                        request != null ? request.getMsgId() : "unknown",
                        request != null ? request.getPhone() : "unknown",
                        request != null ? request.getState() : "unknown");
            } else {
                log.error("短信回调处理失败，msgId：{}，phone：{}，state：{}",
                        request != null ? request.getMsgId() : "unknown",
                        request != null ? request.getPhone() : "unknown",
                        request != null ? request.getState() : "unknown");
            }

            // 无论处理成功还是失败，都返回200状态码表示接收成功
            // 这样可以避免短信服务商重复回调
            return ResponseEntity.ok().build();

        } catch (Exception e) {
            log.error("处理短信回调异常，请求：{}",
                    request != null ? request.getMsgId() : "unknown", e);
            // 即使处理异常，也返回200，避免短信服务商重复回调
            return ResponseEntity.ok().build();
        }
    }
}
