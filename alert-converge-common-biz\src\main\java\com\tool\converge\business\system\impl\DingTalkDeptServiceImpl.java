package com.tool.converge.business.system.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.tool.converge.business.system.DingTalkDeptService;
import com.tool.converge.business.system.DingTalkUserService;
import com.tool.converge.common.enums.OrgnizationTypeEnum;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptQueryParamsBO;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptSaveBO;
import com.tool.converge.repository.domain.system.bo.DingTalkDeptUpdateBO;
import com.tool.converge.repository.domain.system.db.DingTalkDeptDO;
import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptDetailVO;
import com.tool.converge.repository.domain.system.vo.DingTalkDeptPageVO;
import com.tool.converge.repository.domain.system.vo.OrganizationTreeVO;
import com.tool.converge.repository.mapper.system.DingTalkDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 钉钉部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Slf4j
@Service
public class DingTalkDeptServiceImpl extends ServiceImpl<DingTalkDeptMapper, DingTalkDeptDO> implements DingTalkDeptService {

    @Resource
    private DingTalkDeptMapper dingTalkDeptMapper;
    /**
     * 根部门名称
     */
    public static final String ROOT_DEPT_NAME = "广州市全民钱包科技有限公司";

    /**
     * 根部门id
     */
    public static final Long ROOT_DEPT_ID = 1L;

    @Resource
    private DingTalkUserService dingTalkUserService;

    @Override
    public Boolean saveInfo(DingTalkDeptSaveBO saveBO) {
        DingTalkDeptDO entity = new DingTalkDeptDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(dingTalkDeptMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id) {
        return SqlHelper.retBool(dingTalkDeptMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(DingTalkDeptUpdateBO updateBO) {
        DingTalkDeptDO entity = new DingTalkDeptDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(dingTalkDeptMapper.updateById(entity));
    }

    @Override
    public DingTalkDeptDetailVO getInfo(Long id) {
        DingTalkDeptDO entity = dingTalkDeptMapper.selectById(id);
        return DingTalkDeptDetailVO.of(entity);
    }

    @Override
    public IPage<DingTalkDeptPageVO> getPageInfo(DingTalkDeptQueryParamsBO queryParamsBO) {
        return dingTalkDeptMapper.selectPage(queryParamsBO.pageInfo(), queryParamsBO.queryWrapper()).convert(DingTalkDeptPageVO::of);
    }

    @Override
    public List<DingTalkDeptDetailVO> allDept() {
        DingTalkDeptDetailVO root = new DingTalkDeptDetailVO();
        root.setDeptName(ROOT_DEPT_NAME);
        root.setDeptId(ROOT_DEPT_ID);
        List<DingTalkDeptDO> dingTalkDeptList = dingTalkDeptMapper.selectList(null);
        if (!dingTalkDeptList.isEmpty()) {
            Map<Long, List<DingTalkDeptDetailVO>> map = dingTalkDeptList.stream()
                    .map(DingTalkDeptDetailVO::of)
                    .collect(Collectors.groupingBy(DingTalkDeptDetailVO::getParentId));
            root.setUpdateTime(dingTalkDeptList.stream().map(DingTalkDeptDO::getUpdateTime).max(LocalDateTime::compareTo).get());
            fillSubDept(root, map);
        }
        return Collections.singletonList(root);
    }

    /**
     * 填充子部门
     *
     * @param vo
     * @param map
     */
    private void fillSubDept(DingTalkDeptDetailVO vo, Map<Long, List<DingTalkDeptDetailVO>> map) {
        List<DingTalkDeptDetailVO> list = map.getOrDefault(vo.getDeptId(), Collections.emptyList());
        if (!list.isEmpty()) {
            vo.setSubList(list);
            list.forEach(item -> fillSubDept(item, map));
        }
    }

    @Override
    public List<OrganizationTreeVO> getOrganizationTree() {
        // 获取所有的组织架构
        List<DingTalkDeptDetailVO> dingTalkDeptDetailVos = allDept();
        // 获取所有人员
        List<DingTalkUserDO> userDos = dingTalkUserService.list();
        return buildOrderTree(dingTalkDeptDetailVos, userDos);
    }

    private List<OrganizationTreeVO> buildOrderTree(List<DingTalkDeptDetailVO> dingTalkDeptDetailVos, List<DingTalkUserDO> userDos) {
        // 将用户按部门ID分组
        Map<Long, List<DingTalkUserDO>> userGroupByDept = userDos.stream()
                .collect(Collectors.groupingBy(DingTalkUserDO::getDeptId));

        // 转换根部门节点
        List<OrganizationTreeVO> rootNodes = new ArrayList<>();
        for (DingTalkDeptDetailVO dept : dingTalkDeptDetailVos) {
            OrganizationTreeVO node = convertToOrganizationTreeVO(dept, userGroupByDept);
            rootNodes.add(node);
        }

        return rootNodes;
    }


    /**
     * 递归转换DingTalkDeptDetailVO为OrganizationTreeVO
     *
     * @param dept            部门节点
     * @param userGroupByDept 按部门ID分组的用户列表
     * @return OrganizationTreeVO节点
     */
    private OrganizationTreeVO convertToOrganizationTreeVO(DingTalkDeptDetailVO dept, Map<Long, List<DingTalkUserDO>> userGroupByDept) {
        // 创建部门节点
        OrganizationTreeVO node = new OrganizationTreeVO();
        node.setId(dept.getDeptId());
        node.setName(dept.getDeptName());
        node.setType(OrgnizationTypeEnum.DEPT.name());
        // 如果OrganizationTreeVO有其他部门属性，可以在这里设置

        // 为该部门添加用户子节点
        List<DingTalkUserDO> usersInDept = userGroupByDept.get(dept.getDeptId());
        if (usersInDept != null && !usersInDept.isEmpty()) {
            List<OrganizationTreeVO> userNodes = usersInDept.stream()
                    .map(user -> {
                        OrganizationTreeVO userNode = new OrganizationTreeVO();
                        userNode.setId(user.getId());
                        userNode.setName(user.getName()); // 假设有getUserName方法
                        userNode.setType(OrgnizationTypeEnum.USER.name());
                        userNode.setUserId(user.getUserId());
                        userNode.setPost(user.getPost());
                        userNode.setMobile(user.getMobile());
                        userNode.setEmail(user.getEmail());
                        return userNode;
                    })
                    .collect(Collectors.toList());

            // 将用户节点添加到部门节点的子节点中
            node.setChildren(userNodes);
        }

        // 递归转换子部门
        if (dept.getSubList() != null && !dept.getSubList().isEmpty()) {
            List<OrganizationTreeVO> childDeptNodes = dept.getSubList().stream()
                    .map(childDept -> convertToOrganizationTreeVO(childDept, userGroupByDept))
                    .collect(Collectors.toList());

            // 将子部门节点添加到当前部门节点的子节点中
            if (node.getChildren() != null) {
                node.getChildren().addAll(childDeptNodes);
            } else {
                node.setChildren(childDeptNodes);
            }
        }

        return node;
    }

}
