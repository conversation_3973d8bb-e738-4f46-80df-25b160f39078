package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.business.alert.rule.model.ConditionEvaluationDetail;
import com.tool.converge.business.alert.rule.model.ProcessStatus;
import com.tool.converge.business.alert.rule.model.RuleProcessDetail;
import com.tool.converge.business.rules.WarnConditionService;
import com.tool.converge.business.system.SysDictValueService;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import com.tool.converge.repository.domain.system.db.SysDictValueDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 规则处理器
 * 用于处理单个规则的所有条件评估
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class RuleProcessor {

    @Resource
    private WarnConditionService warnConditionService;

    @Resource
    private ConditionEvaluator conditionEvaluator;

    @Resource
    private SysDictValueService sysDictValueService;

    /**
     * 处理单个规则的所有条件（带详细结果）
     * 
     * @param rulesDO 规则配置
     * @param payloadMap 事件数据映射
     * @return 规则处理详情
     */
    public RuleProcessDetail processRuleWithDetail(RulesDO rulesDO, Map<String, String> payloadMap) {
        long startTime = System.currentTimeMillis();
        
        RuleProcessDetail detail = RuleProcessDetail.builder()
            .ruleId(rulesDO.getId())
            .ruleName(rulesDO.getRuleName())
            .ruleCode(rulesDO.getRuleCode())
            .ruleMatching(rulesDO.getRuleMatching())
            .processStartTime(startTime)
            .build();

        try {
            // 1. 查询规则的所有条件
            List<WarnConditionDO> conditions = warnConditionService.list(
                new LambdaQueryWrapper<WarnConditionDO>()
                    .eq(WarnConditionDO::getRulesId, rulesDO.getId())
                    .eq(WarnConditionDO::getDeleted, false)
            );

            detail.setTotalConditionCount(conditions != null ? conditions.size() : 0);

            if (CollectionUtils.isEmpty(conditions)) {
                detail.setStatus(ProcessStatus.FAILED);
                detail.setMessage("规则无条件配置");
                detail.setProcessEndTime(System.currentTimeMillis());
                return detail;
            }

            // 2. 根据规则匹配模式进行评估
            boolean isAllMatch = "1".equals(rulesDO.getRuleMatching());
            int matchedCount = 0;
            Map<String, String> conditionCnMap = sysDictValueService.listByKeyName("fangkuan").stream().collect(Collectors.toMap(SysDictValueDO::getValue, SysDictValueDO::getLabel));
            for (WarnConditionDO condition : conditions) {
                ConditionEvaluationDetail conditionDetail = conditionEvaluator.evaluateConditionWithDetail(condition,conditionCnMap, payloadMap);
                detail.addConditionDetail(conditionDetail);

                if (conditionDetail.isResult()) {
                    matchedCount++;
                }

                if (isAllMatch) {
                    // 满足所有条件模式：任一条件不满足则可以提前结束
                    if (!conditionDetail.isResult()) {
                        detail.setMatchedConditionCount(matchedCount);
                        detail.setStatus(ProcessStatus.FAILED);
                        detail.setMessage("条件不满足（满足所有条件模式）");
                        detail.setProcessEndTime(System.currentTimeMillis());
                        return detail;
                    }
                } else {
                    // 满足任一条件模式：任一条件满足则可以提前结束
                    if (conditionDetail.isResult()) {
                        detail.setMatchedConditionCount(matchedCount);
                        detail.setStatus(ProcessStatus.SUCCESS);
                        detail.setMessage("条件满足（满足任一条件模式）");
                        detail.setProcessEndTime(System.currentTimeMillis());
                        return detail;
                    }
                }
            }

            // 设置最终结果
            detail.setMatchedConditionCount(matchedCount);
            
            if (isAllMatch) {
                // 满足所有条件模式：所有条件都满足
                detail.setStatus(ProcessStatus.SUCCESS);
                detail.setMessage("所有条件都满足");
            } else {
                // 满足任一条件模式：没有条件满足
                detail.setStatus(ProcessStatus.FAILED);
                detail.setMessage("没有条件满足（满足任一条件模式）");
            }

            detail.setProcessEndTime(System.currentTimeMillis());
            return detail;

        } catch (Exception e) {
            log.error("规则处理异常，规则ID：{}，错误：{}", rulesDO.getId(), e.getMessage(), e);
            
            detail.setStatus(ProcessStatus.ERROR);
            detail.setMessage("规则处理异常");
            detail.setErrorMessage(e.getMessage());
            detail.setProcessEndTime(System.currentTimeMillis());
            
            return detail;
        }
    }
}
