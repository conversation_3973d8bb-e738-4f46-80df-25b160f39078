<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.converge.repository.mapper.alert.AlertModelConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.converge.repository.domain.alert.db.AlertModelConfigDO">
        <id column="id" property="id" />
        <result column="model_id" property="modelId" />
        <result column="frequency" property="frequency" />
        <result column="warn_type" property="warnType" />
        <result column="warn_content" property="warnContent" />
        <result column="warn_level" property="warnLevel" />
        <result column="webhook" property="webhook" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 完整预警消息信息复合VO映射结果 -->
    <resultMap id="FullInfoResultMap" type="com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoCompositeVO">
        <!-- 主键映射，提高MyBatis性能 -->
        <id column="config_id" property="alertModelConfig.id" />

        <!-- 预警配置信息 - 使用association -->
        <association property="alertModelConfig" javaType="com.tool.converge.repository.domain.alert.db.AlertModelConfigDO">
            <id column="config_id" property="id" />
            <result column="model_id" property="modelId" />
            <result column="frequency" property="frequency" />
            <result column="warn_type" property="warnType" />
            <result column="warn_content" property="warnContent" />
            <result column="warn_level" property="warnLevel" />
            <result column="webhook" property="webhook" />
            <result column="config_create_time" property="createTime" />
            <result column="config_update_time" property="updateTime" />
            <result column="config_creator" property="creator" />
            <result column="config_updater" property="updater" />
            <result column="config_deleted" property="deleted" />
        </association>

        <!-- 通知人员信息集合 - 使用嵌套查询避免笛卡尔积 -->
        <collection property="notifiers" ofType="com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO"
                    select="selectNotifiersByConfigId" column="config_id" />

        <!-- 用户详细信息集合 - 使用嵌套查询避免笛卡尔积 -->
        <collection property="users" ofType="com.tool.converge.repository.domain.system.db.DingTalkUserDO"
                    select="selectUsersByConfigId" column="config_id" />

        <!-- 模型规则关联集合 - 使用嵌套查询避免笛卡尔积 -->
        <collection property="modelRules" ofType="com.tool.converge.repository.domain.alert.db.AlertModelRuleDO"
                    select="selectModelRulesByModelId" column="model_id" />

        <!-- 规则信息集合 - 使用嵌套查询避免笛卡尔积 -->
        <collection property="rules" ofType="com.tool.converge.repository.domain.rules.db.RulesDO"
                    select="selectRulesByModelId" column="model_id" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, model_id, frequency, warn_type, warn_content, webhook, create_time, update_time, creator, updater, deleted
    </sql>

    <!-- 根据模型ID获取完整的预警配置信息(包括通知人员、规则等关联信息) -->
    <select id="selectFullInfoByModelId" resultMap="FullInfoResultMap">
        SELECT
            amc.id AS config_id,
            amc.model_id,
            amc.frequency,
            amc.warn_type,
            amc.warn_level,
            amc.warn_content,
            amc.webhook,
            amc.create_time AS config_create_time,
            amc.update_time AS config_update_time,
            amc.creator AS config_creator,
            amc.updater AS config_updater,
            amc.deleted AS config_deleted
        FROM t_alert_model_config amc
        WHERE amc.model_id = #{modelId} AND amc.deleted = 0
    </select>

    <!-- 根据配置ID获取通知人员信息 -->
    <select id="selectNotifiersByConfigId" resultType="com.tool.converge.repository.domain.alert.db.AlertModelNotifiersDO">
        SELECT
            user_id AS userId,
            config_id AS configId
        FROM t_alert_model_notifiers
        WHERE config_id = #{configId}
    </select>

    <!-- 根据配置ID获取用户详细信息 -->
    <select id="selectUsersByConfigId" resultType="com.tool.converge.repository.domain.system.db.DingTalkUserDO">
        SELECT
            dtu.id,
            dtu.user_id AS userId,
            dtu.dept_id AS deptId,
            dtu.dept_name AS deptName,
            dtu.name,
            dtu.post,
            dtu.mobile,
            dtu.email,
            dtu.create_time AS createTime,
            dtu.update_time AS updateTime,
            dtu.creator,
            dtu.updater,
            dtu.deleted
        FROM t_alert_model_notifiers amn
                 INNER JOIN t_ding_talk_user dtu ON amn.user_id = dtu.id
        WHERE amn.config_id = #{configId} AND dtu.deleted = 0
    </select>

    <!-- 根据模型ID获取模型规则关联信息 -->
    <select id="selectModelRulesByModelId" resultType="com.tool.converge.repository.domain.alert.db.AlertModelRuleDO">
        SELECT
            rule_id AS ruleId,
            model_id AS modelId
        FROM t_alert_model_rule
        WHERE model_id = #{modelId}
    </select>

    <!-- 根据模型ID获取规则信息 -->
    <select id="selectRulesByModelId" resultType="com.tool.converge.repository.domain.rules.db.RulesDO">
        SELECT
            r.id,
            r.rule_name AS ruleName,
            r.rule_code AS ruleCode,
            r.rule_status AS ruleStatus,
            r.rule_matching AS ruleMatching,
            r.apply_investor AS applyInvestor,
            r.create_time AS createTime,
            r.update_time AS updateTime,
            r.creator,
            r.updater,
            r.deleted
        FROM t_alert_model_rule amr
                 INNER JOIN t_rules r ON amr.rule_id = r.id
        WHERE amr.model_id = #{modelId} AND r.deleted = 0
    </select>

</mapper>
