package com.tool.converge.business.sms.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 模板短信服务配置
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {

    /**
     * 短信服务接口地址
     */
    private String apiUrl;

    /**
     * 授权token，由管理员分配
     */
    private String token;

    /**
     * 系统编码，由管理员分配
     */
    private String systemCode;

    /**
     * 模板编码，由管理员分配,
     * 预警消息模板
     */
    private String templateCode;

    /**
     * 是否测试，默认false
     */
    private Boolean isTest = false;
}
