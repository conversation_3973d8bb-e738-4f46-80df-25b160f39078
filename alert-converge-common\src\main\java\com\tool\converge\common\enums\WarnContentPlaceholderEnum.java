package com.tool.converge.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警内容占位符枚举
 * 用于管理预警消息模板中的占位符
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Getter
@AllArgsConstructor
public enum WarnContentPlaceholderEnum {

    /**
     * 第三方名称
     */
    PLATFORM_NAME("{platformName}", "第三方名称"),

    /**
     * 期次
     */
    PERIOD("{period}", "期次"),

    /**
     * 预警名称
     */
    MODEL_NAME("{modelName}", "预警名称"),

    /**
     * 业务类型
     */
    BUSINESS_TYPE("{businessType}", "业务类型"),

    /**
     * 预警类型
     */
    ALERT_TYPE("{alertType}", "预警类型"),

    /**
     * 触发条件
     */
    TRIGGER_CONDITION("{triggerCondition}", "触发条件"),

    /**
     * 原因
     */
    REASON("{reason}", "原因"),

    /**
     * 上报时间
     */
    REPORTTIME("{reportTime}", "上报时间");

    /**
     * 占位符（带大括号）
     */
    private final String key;

    /**
     * 占位符描述
     */
    private final String label;

    /**
     * 获取占位符格式（带大括号）
     *
     * @return 占位符格式，如：{platformName}
     */
    public String getPlaceholder() {
        return this.key;
    }

    /**
     * 根据占位符格式查找枚举
     *
     * @param placeholder 占位符格式，如：{platformName}
     * @return 对应的枚举，如果未找到返回null
     */
    public static WarnContentPlaceholderEnum findByPlaceholder(String placeholder) {
        if (placeholder == null) {
            return null;
        }
        for (WarnContentPlaceholderEnum placeholderEnum : values()) {
            if (placeholderEnum.getKey().equals(placeholder)) {
                return placeholderEnum;
            }
        }
        return null;
    }
}
