package com.tool.converge.repository.domain.system.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 钉钉用户
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DingTalkUserUpdateBO对象", description = "钉钉用户")
public class DingTalkUserUpdateBO implements Serializable {

    private static final long serialVersionUID = 1L;


    @Schema(description = "id")
    private Long id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "岗位")
    private String post;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

}
