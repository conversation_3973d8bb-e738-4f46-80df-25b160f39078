package com.tool.converge.business.alert.rule;

import com.tool.converge.business.alert.rule.model.ConditionEvaluationDetail;
import com.tool.converge.business.alert.rule.model.ComparisonOperatorEnum;
import com.tool.converge.repository.domain.rules.db.WarnConditionDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 条件评估器
 * 用于评估单个条件是否满足
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class ConditionEvaluator {


    /**
     * 评估单个条件是否满足（带详细结果）
     * 
     * @param condition 条件配置
     * @param payloadMap 事件数据映射
     * @return 条件评估详情
     */
    public ConditionEvaluationDetail evaluateConditionWithDetail(WarnConditionDO condition,Map<String,String> conditionCnMap, Map<String, String> payloadMap) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 验证条件配置的完整性
            if (condition == null) {
                return ConditionEvaluationDetail.failure(null,null , null, null, null, null, "条件配置为空");
            }

            if (StringUtils.isBlank(condition.getSettingItem())) {
                return ConditionEvaluationDetail.failure(condition.getId(), condition.getSettingItem(), conditionCnMap.get(condition.getSettingItem()),
                    condition.getOperator(), null, null, "设置项为空");
            }

            if (StringUtils.isBlank(condition.getOperator())) {
                return ConditionEvaluationDetail.failure(condition.getId(), condition.getSettingItem(), conditionCnMap.get(condition.getSettingItem()),
                    condition.getOperator(), null, null, "运算符为空");
            }

            if (condition.getAssignmentItem() == null) {
                return ConditionEvaluationDetail.failure(condition.getId(), condition.getSettingItem(), conditionCnMap.get(condition.getSettingItem()),
                    condition.getOperator(), null, null, "赋值项为空");
            }

            // 2. 从payload中获取实际值
            String actualValue = payloadMap.get(condition.getSettingItem());
            if (actualValue == null) {
                return ConditionEvaluationDetail.failure(condition.getId(), condition.getSettingItem(), conditionCnMap.get(condition.getSettingItem()),
                    condition.getOperator(), condition.getAssignmentItem().toString(), null, 
                    "payload中未找到设置项");
            }

            // 3. 获取运算符
            ComparisonOperatorEnum operator = ComparisonOperatorEnum.fromCode(condition.getOperator());
            if (operator == null) {
                return ConditionEvaluationDetail.failure(condition.getId(), condition.getSettingItem(), conditionCnMap.get(condition.getSettingItem()),
                    condition.getOperator(), condition.getAssignmentItem().toString(), actualValue, 
                    "不支持的运算符");
            }

            // 4. 执行比较
            String expectedValue = condition.getAssignmentItem().toString();
            boolean result = operator.compare(actualValue, expectedValue);

            // 5. 构建详细结果
            ConditionEvaluationDetail detail = ConditionEvaluationDetail.success(
                condition.getId(), condition.getSettingItem(),conditionCnMap.get(condition.getSettingItem()), condition.getOperator(),
                expectedValue, actualValue, result);
            
            detail.setEvaluationStartTime(startTime);
            detail.setEvaluationEndTime(System.currentTimeMillis());
            
            // 6. 检测数据类型
            detectDataType(detail, actualValue, expectedValue);

            return detail;

        } catch (Exception e) {
            log.error("条件评估异常，条件ID：{}，错误：{}", condition.getId(), e.getMessage(), e);
            
            ConditionEvaluationDetail detail = ConditionEvaluationDetail.failure(
                condition.getId(), 
                condition != null ? condition.getSettingItem() : null,
                conditionCnMap.get(condition.getSettingItem()),
                condition != null ? condition.getOperator() : null, 
                condition != null && condition.getAssignmentItem() != null ? condition.getAssignmentItem().toString() : null, 
                payloadMap != null && condition != null ? payloadMap.get(condition.getSettingItem()) : null, 
                "条件评估异常：" + e.getMessage());
            
            detail.setEvaluationStartTime(startTime);
            detail.setEvaluationEndTime(System.currentTimeMillis());
            
            return detail;
        }
    }

    /**
     * 检测数据类型并设置相关信息
     * 
     * @param detail 条件评估详情
     * @param actualValue 实际值
     * @param expectedValue 期望值
     */
    private void detectDataType(ConditionEvaluationDetail detail, String actualValue, String expectedValue) {
        try {
            // 尝试转换为数值类型
            new BigDecimal(actualValue);
            new BigDecimal(expectedValue);
            detail.setDataType("NUMERIC");
            detail.setTypeConverted(false);
        } catch (NumberFormatException e) {
            detail.setDataType("STRING");
            detail.setTypeConverted(false);
        }
    }
}
