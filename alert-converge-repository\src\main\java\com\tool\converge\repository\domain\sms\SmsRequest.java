package com.tool.converge.repository.domain.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 模板短信发送请求对象
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发送列表，最大100条
     */
    private List<SendItem> sendList;

    /**
     * 请求流水号，要求唯一
     */
    private String requestNo;

    /**
     * 系统编码，由管理员分配
     */
    private String systemCode;

    /**
     * 模板编码，由管理员分配
     */
    private String templateCode;

    /**
     * 授权token，由管理员分配
     */
    private String token;

    /**
     * 是否测试，默认false
     */
    private Boolean isTest;

    /**
     * 用户回传数据
     */
    private String callData;

    /**
     * 发送项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SendItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 发送目标手机号码
         */
        private String phone;

        /**
         * 模板参数列表
         */
        private List<ParamsItem> paramsList;
    }

    /**
     * 模板参数项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParamsItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 参数编码
         */
        private String paramsCode;

        /**
         * 参数编值
         */
        private String paramsValue;
    }
}
