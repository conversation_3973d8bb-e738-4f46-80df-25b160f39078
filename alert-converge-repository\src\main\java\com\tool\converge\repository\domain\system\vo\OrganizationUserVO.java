package com.tool.converge.repository.domain.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 组织架构人员信息，只展示回显数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "OrganizationTreeVO对象", description = "组织架构信息")
public class OrganizationUserVO {

    /**
     * 节点ID（部门ID或用户ID）
     */
    private Long id;

    /**
     * 节点名称（部门名称或用户姓名）
     */
    private String name;

}
