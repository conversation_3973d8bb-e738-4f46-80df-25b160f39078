package com.tool.converge.repository.domain.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 短信回调通知请求对象
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsCallbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息id，对应发送接口的 requestNo
     */
    private String msgId;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 投递状态
     * DELIVRD_SUCCESS - 投递成功
     * DELIVRD_FAIL - 投递失败
     * SEND_FAIL - 发送失败
     */
    private String state;

    /**
     * 状态描述信息
     */
    private String stateMsg;

    /**
     * 回执时间，格式: yyyy-MM-dd HH:mm:ss
     */
    private String receiveTime;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 用户回传数据，短信发送接口传入，则原封不动传回
     */
    private String callData;

    /**
     * 通道编码
     */
    private String smsChannelCode;

    /**
     * 通道编码描述
     */
    private String smsChannelCodeDesc;
}
