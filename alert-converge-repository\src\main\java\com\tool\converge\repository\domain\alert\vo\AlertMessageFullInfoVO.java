package com.tool.converge.repository.domain.alert.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 完整预警消息信息VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "AlertMessageFullInfoVO对象", description = "完整预警消息信息")
public class AlertMessageFullInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "第三方名称")
    private String platformName;

    @Schema(description = "期次")
    private String period;

    @Schema(description = "预警名称")
    private String modelName;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "预警类型")
    private String alertType;

    @Schema(description = "触发条件(规则里面的触发条件)")
    private List<RuleConditionVO> ruleConditions;

    @Schema(description = "原因")
    private String reason;

    @Schema(description = "上报时间")
    private LocalDateTime createTime;

    @Schema(description = "是否关联规则")
    private Boolean related;

    @Schema(description = "是否生成预警消息")
    private Boolean warned;

    @Schema(description = "预警频率")
    private Integer frequency;

    @Schema(description = "预警级别")
    private String warnLevel;

    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;

    @Schema(description = "钉钉群地址")
    private String webhook;

    @Schema(description = "预警通知人员")
    private List<NotifierInfoVO> notifiers;

    @Schema(description = "预警内容")
    private String warnContent;

    @Schema(description = "预警配置id")
    private Long configId;

    @Schema(description = "系统编号")
    private String systemCode;

    @Schema(description = "系统名称")
    private String systemName;

    @Schema(description = "规则id")
    private List<Long> ruleIds;

    @Schema(description = "预警规则")
    private List<String> ruleNames;

    /**
     * <p>
     * 规则条件VO
     * </p>
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(name = "RuleConditionVO对象", description = "规则条件")
    public static class RuleConditionVO implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "规则ID")
        private Long ruleId;

        @Schema(description = "规则名称")
        private String ruleName;

        @Schema(description = "规则编码")
        private String ruleCode;
    }

    /**
     * <p>
     * 通知人员信息VO
     * </p>
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Schema(name = "NotifierInfoVO对象", description = "通知人员信息")
    public static class NotifierInfoVO implements Serializable {
        private static final long serialVersionUID = 1L;

        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "用户昵称")
        private String nickName;

        @Schema(description = "钉钉用户id")
        private String dingtalkUserId;

        @Schema(description = "手机号码")
        private String phonenumber;
    }
}