package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 预警配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12 11:32:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelConfigSaveBO对象", description = "预警配置表")
public class AlertModelConfigSaveBO implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "模型ID")
    private Long modelId;


    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;


    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;


    @Schema(description = "预警内容")
    private String warnContent;


    @Schema(description = "预警级别 HIGH:高,MIDDLE:中，LOW:低")
    private String warnLevel;


    @Schema(description = "钉钉群地址")
    private String webhook;


}
