package com.tool.converge.repository.domain.alert.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 通用预警事件上报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertEventReportBO对象", description = "通用预警事件上报")
public class AlertEventReportBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "系统编号")
    @NotBlank(message = "系统编号不能为空")
    private String systemId;

    @Schema(description = "事件发生时间", example = "2025-01-14T10:30:00.00")
    @NotNull(message = "事件发生时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SS")
    private LocalDateTime eventTime;

    @Schema(description = "模型ID", example = "MODEL_001")
    @NotBlank(message = "模型ID不能为空")
    private String modelId;

    @Schema(description = "事件载荷数据")
    @NotEmpty(message = "载荷数据不能为空")
    @Valid
    private List<PayloadItem> payload;

    /**
     * 载荷项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(name = "PayloadItem", description = "载荷项")
    public static class PayloadItem implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "编码", example = "CPU_USAGE")
        @NotBlank(message = "编码不能为空")
        private String code;

        @Schema(description = "名称", example = "CPU使用率")
        @NotBlank(message = "名称不能为空")
        private String name;

        @Schema(description = "value", example = "85.5")
        @NotNull(message = "value不能为空")
        private String value;
    }
}
