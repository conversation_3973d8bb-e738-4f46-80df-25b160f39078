package com.tool.converge.repository.domain.alert.bo;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.converge.repository.domain.common.PageParamsBO;
import com.tool.converge.repository.domain.alert.db.AlertModelConfigDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 预警配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-12 11:32:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "AlertModelConfigQueryParamsBO对象", description = "预警配置表")
public class AlertModelConfigQueryParamsBO extends PageParamsBO<AlertModelConfigDO> implements Serializable{

    private static final long serialVersionUID = 1L;


    @Schema(description = "预警配置id")
    private Long id;

    @Schema(description = "模型ID")
    private Long modelId;

    @Schema(description = "预警频率 0为不限制")
    private Integer frequency;

    @Schema(description = "预警方式 1:钉钉，2:短信，3:钉钉+短信")
    private Integer warnType;

    @Schema(description = "预警内容")
    private String warnContent;

    @Schema(description = "预警级别 HIGH:高,MIDDLE:中，LOW:低")
    private String warnLevel;

    @Schema(description = "钉钉群地址")
    private String webhook;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "修改时间-开始")
    private LocalDateTime updateTimeStart;

    @Schema(description = "修改时间-结束")
    private LocalDateTime updateTimeEnd;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    private Boolean deleted;

    @Override
    public LambdaQueryWrapper<AlertModelConfigDO> queryWrapper() {

        LambdaQueryWrapper<AlertModelConfigDO> query = new LambdaQueryWrapper<>();

        query.eq(id!=null,AlertModelConfigDO::getId,id);

        query.eq(modelId!=null,AlertModelConfigDO::getModelId,modelId);

        query.eq(frequency!=null,AlertModelConfigDO::getFrequency,frequency);

        query.eq(warnType!=null,AlertModelConfigDO::getWarnType,warnType);

        query.eq(StringUtils.isNotBlank(warnContent),AlertModelConfigDO::getWarnContent,warnContent);

        query.eq(warnLevel!=null,AlertModelConfigDO::getWarnLevel,warnLevel);

        query.eq(StringUtils.isNotBlank(webhook),AlertModelConfigDO::getWebhook,webhook);

        query.ge(createTimeStart != null, AlertModelConfigDO::getCreateTime, createTimeStart);

        query.le(createTimeEnd != null, AlertModelConfigDO::getCreateTime, createTimeEnd);

        query.ge(updateTimeStart != null, AlertModelConfigDO::getUpdateTime, updateTimeStart);

        query.le(updateTimeEnd != null, AlertModelConfigDO::getUpdateTime, updateTimeEnd);

        query.eq(StringUtils.isNotBlank(creator),AlertModelConfigDO::getCreator,creator);

        query.eq(StringUtils.isNotBlank(updater),AlertModelConfigDO::getUpdater,updater);

        query.eq(deleted!=null,AlertModelConfigDO::getDeleted,deleted);

        return query;
    }
}
