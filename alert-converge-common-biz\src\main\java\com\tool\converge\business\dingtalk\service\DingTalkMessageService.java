package com.tool.converge.business.dingtalk.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.tool.converge.repository.domain.dingtalk.DingTalkMessage;
import com.tool.converge.repository.domain.alert.vo.AlertMessageFullInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉消息发送服务
 *
 * <AUTHOR>
 * @since 2025-08-07
 */
@Slf4j
@Service
public class DingTalkMessageService {

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 发送文本消息到指定机器人
     *
     * @param robotUrl 机器人地址
     * @param content  消息内容
     * @param atMobiles @的手机号列表
     */
    public void sendTextMessage(String robotUrl, String content, List<String> atMobiles) {
        DingTalkMessage message = DingTalkMessage.builder()
                .url(robotUrl)
                .msgtype("text")
                .content(content)
                .atMobiles(atMobiles)
                .build();
        sendMessage(message);
    }

    /**
     * 发送钉钉消息
     *
     * @param message 消息对象
     */
    private void sendMessage(DingTalkMessage message) {
        try {
            // 获取最终的URL（如果需要加签会自动处理）
            String finalUrl = getSignedUrl(message.getUrl());

            // 构建HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建钉钉机器人消息格式
            Map<String, Object> requestBody = new HashMap<>(3);
            requestBody.put("msgtype", message.getMsgtype());

            // 文本内容
            Map<String, Object> text = new HashMap<>(1);
            text.put("content", message.getContent());
            requestBody.put("text", text);

            // 处理@功能
            Map<String, Object> at = new HashMap<>(4);
            if (message.getAtMobiles() != null && !message.getAtMobiles().isEmpty()) {
                at.put("atMobiles", message.getAtMobiles());
            }
            if (message.getAtUserIds() != null && !message.getAtUserIds().isEmpty()) {
                at.put("atUserIds", message.getAtUserIds());
            }
            final String isAtAll="true";
            if (isAtAll.equals(message.getIsAtAll())) {
                at.put("isAtAll", true);
            } else {
                at.put("isAtAll", false);
            }
            requestBody.put("at", at);

            // 创建HTTP实体
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            log.info("发送钉钉消息，URL: {}, 内容: {}", finalUrl, JSON.toJSONString(requestBody));

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(finalUrl, entity, String.class);
            log.info("钉钉消息发送响应: {}", response.getBody());

        } catch (Exception e) {
            log.error("发送钉钉消息异常，URL: {}", message.getUrl(), e);
        }
    }

    /**
     * 获取加签后的URL
     * 如果URL中包含secret参数，则进行加签处理
     *
     * @param webhookUrl 原始webhook地址
     * @return 加签后的URL
     */
    private String getSignedUrl(String webhookUrl) {
        try {
            // 检查URL中是否包含secret参数（用于加签）
            final String hasSecretKey1 = "&secret=";
            final String hasSecretKey2 = "?secret=";
            if (!webhookUrl.contains(hasSecretKey1) && !webhookUrl.contains(hasSecretKey2)) {
                // 没有secret参数，直接返回原URL
                return webhookUrl;
            }

            // 提取secret
            String secret = extractSecret(webhookUrl);
            if (StrUtil.isBlank(secret)) {
                return webhookUrl;
            }

            // 移除URL中的secret参数
            String baseUrl = webhookUrl.replaceAll("[&?]secret=[^&]*", "");

            // 生成签名
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;

            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(Base64.getEncoder().encodeToString(signData), StandardCharsets.UTF_8.name());

            // 拼接最终URL
            String connector = baseUrl.contains("?") ? "&" : "?";
            return baseUrl + connector + "timestamp=" + timestamp + "&sign=" + sign;

        } catch (Exception e) {
            log.error("生成钉钉加签URL失败，使用原URL: {}", webhookUrl, e);
            return webhookUrl;
        }
    }

    /**
     * 从URL中提取secret参数
     *
     * @param url webhook URL
     * @return secret值
     */
    private String extractSecret(String url) {
        try {
            String[] parts = url.split("[&?]");
            for (String part : parts) {
                if (part.startsWith("secret=")) {
                    return part.substring(7);
                }
            }
        } catch (Exception e) {
            log.error("提取secret参数失败: {}", url, e);
        }
        return null;
    }
}
