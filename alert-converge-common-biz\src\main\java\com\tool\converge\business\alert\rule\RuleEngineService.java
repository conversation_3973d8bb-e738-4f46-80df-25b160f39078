package com.tool.converge.business.alert.rule;

import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;

import java.util.List;

/**
 * 规则引擎服务接口
 * 用于评估预警事件是否匹配配置的规则
 * 
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface RuleEngineService {

    /**
     * 评估预警事件是否匹配指定的已启动规则
     *
     * <p>该方法直接接受已查询和过滤的启动规则列表，避免重复查询数据库</p>
     *
     * @param alertEventDO 预警事件，包含事件ID、payload等信息
     * @param activeRules 已启动的规则列表
     * @return 规则评估结果，包含是否匹配、匹配的规则信息、处理详情等
     *
     * @throws IllegalArgumentException 当预警事件为null或数据不完整时
     * @throws RuntimeException 当发生系统异常时（如JSON解析异常等）
     */
    RuleEvaluationResult evaluateRulesWithActiveRules(AlertEventDO alertEventDO, List<RulesDO> activeRules);

    /**
     * 检查预警事件数据的完整性
     * 
     * <p>验证预警事件是否包含规则评估所需的基本信息：</p>
     * <ul>
     *   <li>事件ID不能为null</li>
     *   <li>payload不能为空</li>
     *   <li>其他必要字段的完整性</li>
     * </ul>
     * 
     * @param alertEventDO 预警事件
     * @return 是否有效，true表示数据完整，false表示数据不完整
     */
    boolean isValidAlertEvent(AlertEventDO alertEventDO);

}
