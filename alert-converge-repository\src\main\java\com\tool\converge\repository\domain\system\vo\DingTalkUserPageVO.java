package com.tool.converge.repository.domain.system.vo;

import com.tool.converge.repository.domain.system.db.DingTalkUserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 钉钉用户
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-07 08:48:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "DingTalkUserPageVO对象", description = "钉钉用户")
public class DingTalkUserPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "岗位")
    private String post;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "更新人")
    private String updater;

    @Schema(description = "是否删除 0未删除 null已删除")
    private Boolean deleted;

    public static DingTalkUserPageVO of(DingTalkUserDO entity) {
        if (entity == null) {
            return null;
        }
        DingTalkUserPageVO pageVO = new DingTalkUserPageVO();
        BeanUtils.copyProperties(entity, pageVO);
        return pageVO;
    }

}
