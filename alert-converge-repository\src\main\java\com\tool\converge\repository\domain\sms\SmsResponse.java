package com.tool.converge.repository.domain.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 模板短信发送响应对象
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码 (0000-成功，其他-失败)
     */
    private String code;

    /**
     * 错误信息
     */
    private String msg;
}
