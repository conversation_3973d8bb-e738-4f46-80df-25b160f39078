package com.tool.converge.business.alert.rule;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;


import com.tool.converge.business.alert.rule.model.ProcessStatus;
import com.tool.converge.business.alert.rule.model.RuleEvaluationResult;
import com.tool.converge.business.alert.rule.model.RuleProcessDetail;
import com.tool.converge.business.rules.RulesService;
import com.tool.converge.repository.domain.alert.db.AlertEventDO;
import com.tool.converge.repository.domain.rules.db.RulesDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 预警规则引擎实现类
 * 用于评估预警事件是否匹配配置的规则
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Slf4j
@Component
public class RuleEngineServiceImpl implements RuleEngineService {

    @Resource
    private PayloadParser payloadParser;

    @Resource
    private CapitalMatcher capitalMatcher;

    @Resource
    private RuleProcessor ruleProcessor;

    /**
     * 评估预警事件是否匹配指定的已启动规则
     *
     * @param alertEventDO 预警事件
     * @param activeRules 已启动的规则列表
     * @return 规则评估结果
     */
    @Override
    public RuleEvaluationResult evaluateRulesWithActiveRules(AlertEventDO alertEventDO, List<RulesDO> activeRules) {
        long startTime = System.currentTimeMillis();

        // 1. 参数验证
        if (!isValidAlertEvent(alertEventDO)) {
            throw new IllegalArgumentException("预警事件数据不完整");
        }
        if (activeRules == null || activeRules.isEmpty()) {
            log.info("无启动的规则需要评估，事件ID：{}", alertEventDO.getEventId());
            return RuleEvaluationResult.failure("无启动的规则需要评估");
        }

        // 2. 解析payload数据
        Map<String, String> payloadMap = parseEventPayload(alertEventDO);

        // 3. 评估所有规则
        RuleEvaluationContext context = evaluateAllRules(activeRules, payloadMap);

        // 4. 构建并返回评估结果
        return buildEvaluationResult(context, alertEventDO.getId(), startTime);
    }

    /**
     * 解析事件payload数据
     */
    private Map<String, String> parseEventPayload(AlertEventDO alertEventDO) {
        try {
            Map<String, String> payloadMap = payloadParser.parsePayload(alertEventDO.getPayload());
            if (payloadMap == null || payloadMap.isEmpty()) {
                throw new RuntimeException("payload解析失败或为空");
            }
            return payloadMap;
        } catch (Exception e) {
            throw new RuntimeException("payload解析异常", e);
        }
    }

    /**
     * 评估所有规则
     */
    private RuleEvaluationContext evaluateAllRules(List<RulesDO> targetRules, Map<String, String> payloadMap) {
        List<RuleProcessDetail> processDetails = new ArrayList<>();
        List<RulesDO> matchedRules = new ArrayList<>();

        for (RulesDO rule : targetRules) {
            try {
                // 1. 资方匹配验证
                boolean capitalMatched = capitalMatcher.matchCapital(payloadMap, rule);
                RuleProcessDetail detail;
                if (!capitalMatched) {
                    detail = createSkippedRuleDetail(rule, "资方不匹配，跳过规则评估");
                }else {
                    // 2. 条件评估
                    detail = ruleProcessor.processRuleWithDetail(rule, payloadMap);
                    detail.setCapitalMatched(capitalMatched);
                    detail.setCapitalMatchMessage("资方匹配成功");
                }
                processDetails.add(detail);

                if (detail.isMatched()) {
                    matchedRules.add(rule);
                    log.info("规则匹配成功，规则ID：{}，规则名称：{}", rule.getId(), rule.getRuleName());
                } else {
                    log.debug("规则匹配失败，规则ID：{}，规则名称：{}，原因：{}",
                        rule.getId(), rule.getRuleName(), detail.getMessage());
                }
            } catch (Exception e) {
                log.error("规则评估异常，规则ID：{}，规则名称：{}，错误：{}",
                    rule.getId(), rule.getRuleName(), e.getMessage(), e);
                RuleProcessDetail errorDetail = RuleProcessDetail.error(
                    rule.getId(), rule.getRuleName(), rule.getRuleCode(),
                    "规则评估异常：" + e.getMessage());
                processDetails.add(errorDetail);
            }
        }

        return new RuleEvaluationContext(processDetails, matchedRules);
    }

    /**
     * 创建跳过的规则详情
     */
    private RuleProcessDetail createSkippedRuleDetail(RulesDO rule, String message) {
        RuleProcessDetail detail = RuleProcessDetail.builder()
            .ruleId(rule.getId())
            .ruleName(rule.getRuleName())
            .ruleCode(rule.getRuleCode())
            .ruleMatching(rule.getRuleMatching())
            .capitalMatched(false)
            .capitalMatchMessage("资方不匹配")
            .status(ProcessStatus.SKIPPED)
            .message(message)
            .processStartTime(System.currentTimeMillis())
            .processEndTime(System.currentTimeMillis())
            .build();

        log.debug("规则资方不匹配，跳过评估，规则ID：{}，规则名称：{}", rule.getId(), rule.getRuleName());
        return detail;
    }

    /**
     * 构建评估结果
     */
    private RuleEvaluationResult buildEvaluationResult(RuleEvaluationContext context, Long eventId, long startTime) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        if (CollectionUtils.isNotEmpty(context.getMatchedRules())) {
            return buildSuccessResult(context, eventId, duration);
        } else {
            return buildFailureResult(context, eventId, duration);
        }
    }

    /**
     * 构建成功结果
     */
    private RuleEvaluationResult buildSuccessResult(RuleEvaluationContext context, Long eventId, long duration) {
        List<RulesDO> matchedRules = context.getMatchedRules();
        log.info("预警事件规则评估完成，匹配{}个规则，事件ID：{}，耗时：{}ms",
            matchedRules.size(), eventId, duration);

        // 使用第一个匹配的规则创建成功结果
        RulesDO firstMatchedRule = matchedRules.get(0);
        RuleEvaluationResult result = RuleEvaluationResult.success(
            firstMatchedRule.getId(),
            firstMatchedRule.getRuleName(),
            firstMatchedRule.getRuleCode(),
                // 默认预警级别，可以根据实际需求调整
            "HIGH"
        );
        result.setProcessDetails(context.getProcessDetails());
        return result;
    }

    /**
     * 构建失败结果
     */
    private RuleEvaluationResult buildFailureResult(RuleEvaluationContext context, Long eventId, long duration) {
        log.info("预警事件规则评估完成，无匹配规则，事件ID：{}，耗时：{}ms", eventId, duration);
        RuleEvaluationResult result = RuleEvaluationResult.failure("无匹配规则");
        result.setProcessDetails(context.getProcessDetails());
        return result;
    }

    /**
     * 规则评估上下文
     */
    private static class RuleEvaluationContext {
        private final List<RuleProcessDetail> processDetails;
        private final List<RulesDO> matchedRules;

        public RuleEvaluationContext(List<RuleProcessDetail> processDetails, List<RulesDO> matchedRules) {
            this.processDetails = processDetails;
            this.matchedRules = matchedRules;
        }

        public List<RuleProcessDetail> getProcessDetails() {
            return processDetails;
        }

        public List<RulesDO> getMatchedRules() {
            return matchedRules;
        }
    }

    /**
     * 检查预警事件数据的完整性
     * 
     * @param alertEventDO 预警事件
     * @return 检查结果
     */
    @Override
    public boolean isValidAlertEvent(AlertEventDO alertEventDO) {
        if (alertEventDO == null) {
            return false;
        }

        if (alertEventDO.getId() == null) {
            return false;
        }

        if (StringUtils.isBlank(alertEventDO.getPayload())) {
            return false;
        }

        return true;
    }
}
